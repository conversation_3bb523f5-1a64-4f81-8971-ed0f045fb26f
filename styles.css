/* CSS Variables for Theme Support */
:root {
  /* Light theme colors */
  --background: #ffffff;
  --foreground: #0f172a;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --primary: #22c55e;
  --primary-foreground: #ffffff;
  --secondary: #3b82f6;
  --secondary-foreground: #ffffff;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #f59e0b;
  --accent-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #22c55e;
  
  /* Typing test specific colors */
  --correct-char: #22c55e;
  --correct-char-bg: #f0fdf4;
  --incorrect-char: #ef4444;
  --incorrect-char-bg: #fef2f2;
  --current-char: #eab308;
  --current-char-bg: #fefce8;
  --untyped-char: #64748b;
  --cursor: #22c55e;
  
  /* Spacing and sizing */
  --radius: 0.5rem;
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.dark {
  /* Dark theme colors */
  --background: #0f172a;
  --foreground: #f8fafc;
  --card: #0f172a;
  --card-foreground: #f8fafc;
  --primary: #22c55e;
  --primary-foreground: #ffffff;
  --secondary: #3b82f6;
  --secondary-foreground: #ffffff;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --accent: #f59e0b;
  --accent-foreground: #0f172a;
  --border: #1e293b;
  --input: #1e293b;
  --ring: #22c55e;
  
  /* Typing test specific colors - dark theme */
  --correct-char: #22c55e;
  --correct-char-bg: #052e16;
  --incorrect-char: #ef4444;
  --incorrect-char-bg: #450a0a;
  --current-char: #eab308;
  --current-char-bg: #422006;
  --untyped-char: #94a3b8;
  --cursor: #22c55e;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'JetBrains Mono', monospace;
  background-color: var(--background);
  color: var(--foreground);
  line-height: 1.6;
  transition: background-color 0.15s ease, color 0.15s ease;
}

.app {
  min-height: 100vh;
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header Styles */
.header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.title-section {
  text-align: center;
  flex: 1;
}

.main-title {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary), var(--secondary), var(--accent));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1.125rem;
  color: var(--muted-foreground);
}

/* Theme Toggle */
.theme-toggle {
  display: flex;
  background-color: var(--muted);
  border-radius: var(--radius);
  padding: 0.25rem;
  gap: 0.25rem;
  transition: background-color 0.15s ease;
}

.theme-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: none;
  border-radius: calc(var(--radius) - 0.125rem);
  background: transparent;
  color: var(--muted-foreground);
  cursor: pointer;
  transition: all 0.15s ease;
}

.theme-btn:hover {
  background-color: var(--background);
  color: var(--foreground);
  box-shadow: var(--shadow);
}

.theme-btn.active {
  background-color: var(--background);
  color: var(--foreground);
  box-shadow: var(--shadow);
}

/* Settings Panel */
.settings-panel {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-lg);
  transition: background-color 0.15s ease, border-color 0.15s ease;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.setting-label {
  font-weight: 600;
  color: var(--card-foreground);
  font-size: 0.875rem;
}

.setting-select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--input);
  border-radius: var(--radius);
  background-color: var(--background);
  color: var(--foreground);
  font-size: 1rem;
  transition: all 0.15s ease;
}

.setting-select:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1.5rem;
  text-align: center;
  box-shadow: var(--shadow-lg);
  transition: all 0.15s ease;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  color: var(--primary);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--card-foreground);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--muted-foreground);
}

/* Text Section */
.text-section {
  margin-bottom: 2rem;
}

.passage-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: 1rem;
}

.text-display {
  background-color: var(--card);
  border: 2px solid var(--border);
  border-radius: var(--radius);
  padding: 2rem;
  font-size: 1.25rem;
  line-height: 1.8;
  min-height: 200px;
  max-height: 300px;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: all 0.15s ease;
}

/* Typing Character Styles */
.typing-char {
  position: relative;
}

.typing-char.correct {
  color: var(--correct-char);
  background-color: var(--correct-char-bg);
}

.typing-char.incorrect {
  color: var(--incorrect-char);
  background-color: var(--incorrect-char-bg);
}

.typing-char.current {
  color: var(--current-char);
  background-color: var(--current-char-bg);
  animation: pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.typing-char.untyped {
  color: var(--untyped-char);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Input Section */
.input-section {
  margin-bottom: 2rem;
}

.typing-input {
  width: 100%;
  padding: 1.5rem;
  border: 2px solid var(--input);
  border-radius: var(--radius);
  background-color: var(--background);
  color: var(--foreground);
  font-size: 1.125rem;
  font-family: inherit;
  resize: none;
  transition: all 0.15s ease;
}

.typing-input:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
}

.typing-input:disabled {
  background-color: var(--muted);
  color: var(--muted-foreground);
  cursor: not-allowed;
}

/* Controls */
.controls {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.reset-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: var(--primary);
  color: var(--primary-foreground);
  border: none;
  border-radius: var(--radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.reset-btn:hover {
  background-color: var(--primary);
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-overlay.hidden {
  display: none;
}

.modal-content {
  background-color: var(--card);
  border-radius: var(--radius);
  box-shadow: var(--shadow-lg);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border);
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--card-foreground);
}

.close-btn {
  background: none;
  border: none;
  color: var(--muted-foreground);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--radius);
  transition: all 0.15s ease;
}

.close-btn:hover {
  color: var(--foreground);
  background-color: var(--muted);
}

.modal-body {
  padding: 1.5rem;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.result-item {
  text-align: center;
  padding: 1rem;
  background-color: var(--muted);
  border-radius: var(--radius);
}

.result-label {
  font-size: 0.875rem;
  color: var(--muted-foreground);
  margin-bottom: 0.5rem;
}

.result-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--card-foreground);
}

.modal-actions {
  display: flex;
  justify-content: center;
}

.primary-btn {
  padding: 0.75rem 2rem;
  background-color: var(--primary);
  color: var(--primary-foreground);
  border: none;
  border-radius: var(--radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.primary-btn:hover {
  background-color: var(--primary);
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 1rem 0.5rem;
  }
  
  .main-title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .text-display {
    padding: 1rem;
    font-size: 1rem;
  }
  
  .typing-input {
    padding: 1rem;
    font-size: 1rem;
  }
  
  .results-grid {
    grid-template-columns: 1fr;
  }
  
  /* Improve touch targets */
  button, select, textarea, input {
    min-height: 44px;
  }
  
  /* Prevent zoom on input focus */
  input, textarea, select {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .text-display {
    font-size: 0.875rem;
    min-height: 150px;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 4px;
  opacity: 0.3;
}

::-webkit-scrollbar-thumb:hover {
  opacity: 0.5;
}

/* Selection styles */
::selection {
  background-color: rgba(34, 197, 94, 0.2);
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}
