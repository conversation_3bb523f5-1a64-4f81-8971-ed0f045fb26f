<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Easy Typing - Test and improve your typing speed</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app">
        <!-- Header with Theme Toggle -->
        <div class="header">
            <div class="header-content">
                <div class="title-section">
                    <h1 class="main-title">Easy Typing</h1>
                    <p class="subtitle">Test and improve your typing speed with precision</p>
                </div>
                <div class="theme-toggle-container">
                    <div class="theme-toggle">
                        <button class="theme-btn" data-theme="light" title="Light theme">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="5"/>
                                <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
                            </svg>
                        </button>
                        <button class="theme-btn" data-theme="dark" title="Dark theme">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                            </svg>
                        </button>
                        <button class="theme-btn" data-theme="system" title="System theme">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                                <line x1="8" y1="21" x2="16" y2="21"/>
                                <line x1="12" y1="17" x2="12" y2="21"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Panel -->
        <div class="settings-panel">
            <div class="settings-grid">
                <!-- Duration Selection -->
                <div class="setting-group">
                    <label for="duration-select" class="setting-label">Test Duration</label>
                    <select id="duration-select" class="setting-select">
                        <option value="30">30 seconds</option>
                        <option value="60" selected>60 seconds</option>
                        <option value="120">2 minutes</option>
                        <option value="300">5 minutes</option>
                    </select>
                </div>

                <!-- Difficulty Selection -->
                <div class="setting-group">
                    <label for="difficulty-select" class="setting-label">Difficulty Level</label>
                    <select id="difficulty-select" class="setting-select">
                        <option value="beginner">Beginner</option>
                        <option value="intermediate" selected>Intermediate</option>
                        <option value="advanced">Advanced</option>
                    </select>
                </div>

                <!-- Passage Selection -->
                <div class="setting-group">
                    <label for="passage-select" class="setting-label">Text Passage</label>
                    <select id="passage-select" class="setting-select">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                </div>
            </div>
        </div>

        <!-- Stats Display -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <polyline points="12,6 12,12 16,14"/>
                    </svg>
                </div>
                <div class="stat-value" id="time-left">60</div>
                <div class="stat-label">Time Left</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>
                    </svg>
                </div>
                <div class="stat-value" id="wpm">0</div>
                <div class="stat-label">WPM</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 11l3 3l8-8"/>
                        <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9s4.03-9 9-9c1.51 0 2.93.37 4.18 1.03"/>
                    </svg>
                </div>
                <div class="stat-value" id="accuracy">100</div>
                <div class="stat-label">Accuracy %</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M6 9l6 6l6-6"/>
                    </svg>
                </div>
                <div class="stat-value" id="characters">0</div>
                <div class="stat-label">Characters</div>
            </div>
        </div>

        <!-- Text Display -->
        <div class="text-section">
            <h3 id="passage-title" class="passage-title">Loading...</h3>
            <div id="text-display" class="text-display">
                <!-- Text will be populated by JavaScript -->
            </div>
        </div>

        <!-- Input Area -->
        <div class="input-section">
            <textarea 
                id="typing-input" 
                class="typing-input" 
                placeholder="Click here and start typing to begin the test"
                rows="4"
            ></textarea>
        </div>

        <!-- Reset Button -->
        <div class="controls">
            <button id="reset-btn" class="reset-btn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="1,4 1,10 7,10"/>
                    <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                </svg>
                Reset Test
            </button>
        </div>

        <!-- Results Modal -->
        <div id="results-modal" class="modal-overlay hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title">Test Results</h2>
                    <button id="close-modal" class="close-btn">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"/>
                            <line x1="6" y1="6" x2="18" y2="18"/>
                        </svg>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="results-grid">
                        <div class="result-item">
                            <div class="result-label">Words Per Minute</div>
                            <div class="result-value" id="final-wpm">0</div>
                        </div>
                        <div class="result-item">
                            <div class="result-label">Accuracy</div>
                            <div class="result-value" id="final-accuracy">100%</div>
                        </div>
                        <div class="result-item">
                            <div class="result-label">Total Characters</div>
                            <div class="result-value" id="final-characters">0</div>
                        </div>
                        <div class="result-item">
                            <div class="result-label">Correct Characters</div>
                            <div class="result-value" id="final-correct">0</div>
                        </div>
                        <div class="result-item">
                            <div class="result-label">Incorrect Characters</div>
                            <div class="result-value" id="final-incorrect">0</div>
                        </div>
                        <div class="result-item">
                            <div class="result-label">Time Elapsed</div>
                            <div class="result-value" id="final-time">0s</div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button id="try-again-btn" class="primary-btn">Try Again</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
