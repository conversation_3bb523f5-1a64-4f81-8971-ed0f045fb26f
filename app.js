// Typing Test Application
class TypingTest {
    constructor() {
        this.passages = null;
        this.currentPassage = null;
        this.settings = {
            duration: 60,
            difficulty: 'intermediate'
        };
        
        // Test state
        this.input = '';
        this.isActive = false;
        this.isCompleted = false;
        this.timeLeft = this.settings.duration;
        this.startTime = null;
        this.characterStatuses = [];
        this.timer = null;
        
        // DOM elements
        this.elements = {};
        
        this.init();
    }
    
    async init() {
        console.log('Initializing Typing Test...');
        this.bindElements();
        this.bindEvents();
        await this.loadPassages();
        this.initializeTheme();
        this.updateDisplay();
        console.log('Typing Test initialized successfully');
    }
    
    bindElements() {
        this.elements = {
            // Settings
            durationSelect: document.getElementById('duration-select'),
            difficultySelect: document.getElementById('difficulty-select'),
            passageSelect: document.getElementById('passage-select'),
            
            // Stats
            timeLeft: document.getElementById('time-left'),
            wpm: document.getElementById('wpm'),
            accuracy: document.getElementById('accuracy'),
            characters: document.getElementById('characters'),
            
            // Text display
            passageTitle: document.getElementById('passage-title'),
            textDisplay: document.getElementById('text-display'),
            typingInput: document.getElementById('typing-input'),
            
            // Controls
            resetBtn: document.getElementById('reset-btn'),
            
            // Modal
            resultsModal: document.getElementById('results-modal'),
            closeModal: document.getElementById('close-modal'),
            tryAgainBtn: document.getElementById('try-again-btn'),
            
            // Final results
            finalWpm: document.getElementById('final-wpm'),
            finalAccuracy: document.getElementById('final-accuracy'),
            finalCharacters: document.getElementById('final-characters'),
            finalCorrect: document.getElementById('final-correct'),
            finalIncorrect: document.getElementById('final-incorrect'),
            finalTime: document.getElementById('final-time'),
            
            // Theme
            themeBtns: document.querySelectorAll('.theme-btn')
        };
    }
    
    bindEvents() {
        // Settings
        this.elements.durationSelect.addEventListener('change', (e) => {
            this.handleDurationChange(parseInt(e.target.value));
        });
        
        this.elements.difficultySelect.addEventListener('change', (e) => {
            this.handleDifficultyChange(e.target.value);
        });
        
        this.elements.passageSelect.addEventListener('change', (e) => {
            this.handlePassageChange(parseInt(e.target.value));
        });
        
        // Typing input
        this.elements.typingInput.addEventListener('input', (e) => {
            this.handleInputChange(e.target.value);
        });
        
        // Controls
        this.elements.resetBtn.addEventListener('click', () => {
            this.reset();
        });
        
        // Modal
        this.elements.closeModal.addEventListener('click', () => {
            this.hideResults();
        });
        
        this.elements.tryAgainBtn.addEventListener('click', () => {
            this.hideResults();
            this.reset();
        });
        
        // Theme
        this.elements.themeBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                this.setTheme(btn.dataset.theme);
            });
        });
        
        // Modal overlay click
        this.elements.resultsModal.addEventListener('click', (e) => {
            if (e.target === this.elements.resultsModal) {
                this.hideResults();
            }
        });
    }
    
    async loadPassages() {
        try {
            const response = await fetch('data/passages.json');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.passages = await response.json();
            this.updatePassageOptions();
            this.setInitialPassage();
        } catch (error) {
            console.error('Error loading passages:', error);
            // Fallback data
            this.passages = {
                beginner: [{
                    id: 1,
                    title: "Simple Introduction",
                    text: "The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet."
                }],
                intermediate: [{
                    id: 2,
                    title: "Technology Evolution",
                    text: "The advancement of technology has revolutionized how we communicate and work. Modern keyboards respond instantly to our touch."
                }],
                advanced: [{
                    id: 3,
                    title: "Complex Syntax",
                    text: "Programming languages utilize intricate syntax patterns including brackets [], parentheses (), and various operators like +=, -=, *=."
                }]
            };
            this.updatePassageOptions();
            this.setInitialPassage();
        }
    }
    
    updatePassageOptions() {
        const select = this.elements.passageSelect;
        select.innerHTML = '';
        
        const passages = this.passages[this.settings.difficulty];
        passages.forEach(passage => {
            const option = document.createElement('option');
            option.value = passage.id;
            option.textContent = passage.title;
            select.appendChild(option);
        });
    }
    
    setInitialPassage() {
        if (this.passages && this.passages[this.settings.difficulty]) {
            this.currentPassage = this.passages[this.settings.difficulty][0];
            this.initializeCharacterStatuses();
            this.updateDisplay();
        }
    }
    
    initializeCharacterStatuses() {
        if (this.currentPassage) {
            this.characterStatuses = new Array(this.currentPassage.text.length).fill('pending');
        }
    }
    
    handleDurationChange(duration) {
        this.settings.duration = duration;
        this.reset();
    }
    
    handleDifficultyChange(difficulty) {
        this.settings.difficulty = difficulty;
        this.updatePassageOptions();
        if (this.passages) {
            this.currentPassage = this.passages[difficulty][0];
            this.reset();
        }
    }
    
    handlePassageChange(passageId) {
        if (this.passages) {
            const passage = this.passages[this.settings.difficulty].find(p => p.id === passageId);
            if (passage) {
                this.currentPassage = passage;
                this.reset();
            }
        }
    }
    
    handleInputChange(value) {
        if (this.isCompleted) return;

        // Start the test on first input
        if (!this.isActive && !this.startTime) {
            this.startTest();
        }

        this.input = value;
        this.updateCharacterStatuses();
        this.updateStats();

        // Check if completed (passage finished or time up)
        if (value.length === this.currentPassage.text.length) {
            this.completeTest();
        }

        this.updateTextDisplay();
    }
    
    startTest() {
        this.isActive = true;
        this.startTime = Date.now();
        this.elements.typingInput.placeholder = "Keep typing...";
        this.startTimer();
    }
    
    startTimer() {
        this.timer = setInterval(() => {
            this.timeLeft--;
            this.updateDisplay();
            
            if (this.timeLeft <= 0) {
                this.completeTest();
            }
        }, 1000);
    }
    
    completeTest() {
        this.isCompleted = true;
        this.isActive = false;
        this.elements.typingInput.disabled = true;
        
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
        
        this.showResults();
    }
    
    updateCharacterStatuses() {
        const text = this.currentPassage.text;
        const inputLength = this.input.length;

        // Only update the character that was just typed
        if (inputLength > 0) {
            const lastIndex = inputLength - 1;
            this.characterStatuses[lastIndex] = this.input[lastIndex] === text[lastIndex] ? 'correct' : 'incorrect';
        }
    }
    
    calculateStats() {
        const totalCharacters = this.input.length;
        const correctCharacters = this.characterStatuses.slice(0, totalCharacters)
            .filter(status => status === 'correct').length;
        const incorrectCharacters = totalCharacters - correctCharacters;

        const accuracy = totalCharacters > 0 ? Math.round((correctCharacters / totalCharacters) * 100) : 100;

        const timeElapsed = this.isActive || this.isCompleted
            ? (this.settings.duration - this.timeLeft)
            : 0;

        const minutes = timeElapsed / 60;
        const words = correctCharacters / 5; // Standard: 5 characters = 1 word
        const wpm = minutes > 0 ? Math.round(words / minutes) : 0;

        return {
            wpm,
            accuracy,
            totalCharacters,
            correctCharacters,
            incorrectCharacters,
            timeElapsed
        };
    }
    
    updateStats() {
        const stats = this.calculateStats();
        
        this.elements.wpm.textContent = stats.wpm;
        this.elements.accuracy.textContent = stats.accuracy;
        this.elements.characters.textContent = stats.totalCharacters;
    }
    
    updateDisplay() {
        this.elements.timeLeft.textContent = this.timeLeft;
        
        if (this.currentPassage) {
            this.elements.passageTitle.textContent = this.currentPassage.title;
            this.updateTextDisplay();
        }
        
        this.updateStats();
    }
    
    updateTextDisplay() {
        if (!this.currentPassage) return;

        const text = this.currentPassage.text;
        const display = this.elements.textDisplay;

        // Only create DOM elements once when passage changes
        if (!this.textSpans || this.textSpans.length !== text.length) {
            display.innerHTML = '';
            this.textSpans = [];

            text.split('').forEach((char, index) => {
                const span = document.createElement('span');
                span.textContent = char;
                span.className = 'typing-char untyped';
                this.textSpans.push(span);
                display.appendChild(span);
            });
        }

        // Only update classes for characters that need updating
        const inputLength = this.input.length;

        // Update only the range that might have changed
        const startIndex = Math.max(0, inputLength - 1);
        const endIndex = Math.min(text.length, inputLength + 2);

        for (let i = startIndex; i < endIndex; i++) {
            const span = this.textSpans[i];
            if (!span) continue;

            if (i < inputLength) {
                span.className = 'typing-char ' + this.characterStatuses[i];
            } else if (i === inputLength && inputLength < text.length) {
                span.className = 'typing-char current';
            } else {
                span.className = 'typing-char untyped';
            }
        }
    }
    
    showResults() {
        const stats = this.calculateStats();
        
        this.elements.finalWpm.textContent = stats.wpm;
        this.elements.finalAccuracy.textContent = `${stats.accuracy}%`;
        this.elements.finalCharacters.textContent = stats.totalCharacters;
        this.elements.finalCorrect.textContent = stats.correctCharacters;
        this.elements.finalIncorrect.textContent = stats.incorrectCharacters;
        this.elements.finalTime.textContent = `${stats.timeElapsed}s`;
        
        this.elements.resultsModal.classList.remove('hidden');
    }
    
    hideResults() {
        this.elements.resultsModal.classList.add('hidden');
    }
    
    reset() {
        this.input = '';
        this.isActive = false;
        this.isCompleted = false;
        this.timeLeft = this.settings.duration;
        this.startTime = null;

        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }

        this.elements.typingInput.value = '';
        this.elements.typingInput.disabled = false;
        this.elements.typingInput.placeholder = "Click here and start typing to begin the test";

        // Reset text spans to force recreation
        this.textSpans = null;

        this.initializeCharacterStatuses();
        this.updateDisplay();
        this.hideResults();

        // Focus the input
        setTimeout(() => {
            this.elements.typingInput.focus();
        }, 100);
    }
    
    // Theme Management
    initializeTheme() {
        const savedTheme = localStorage.getItem('typing-test-theme') || 'system';
        this.setTheme(savedTheme);
    }
    
    setTheme(theme) {
        localStorage.setItem('typing-test-theme', theme);
        
        // Update active button
        this.elements.themeBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.theme === theme);
        });
        
        // Apply theme
        const root = document.documentElement;
        root.classList.remove('light', 'dark');
        
        if (theme === 'system') {
            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            root.classList.add(systemTheme);
        } else {
            root.classList.add(theme);
        }
        
        // Set color-scheme for browser UI
        root.style.colorScheme = theme === 'system' 
            ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
            : theme;
    }
}

// Store app instance globally for theme changes
let typingTestApp = null;

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    typingTestApp = new TypingTest();
});

// Listen for system theme changes
window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
    const currentTheme = localStorage.getItem('typing-test-theme');
    if (currentTheme === 'system' && typingTestApp) {
        typingTestApp.setTheme('system');
    }
});
